"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  ArrowLeft, FlaskConical, BarChart3, TrendingUp, TrendingDown,
  CheckCircle, AlertTriangle, Target, Calendar, Download,
  RefreshCw, Search, Filter, Award, Gauge
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { ThemeToggle } from "@/components/theme-toggle";
import { HamburgerMenu } from "@/components/hamburger-menu";
import { Footer } from "@/components/ui/footer";

// 质量检测数据接口
interface QualityData {
  id: string;
  timestamp: string;
  batchNumber: string;
  productType: string;
  pbGrade: number; // 铅品位 (%)
  znGrade: number; // 锌品位 (%)
  moisture: number; // 水分 (%)
  particleSize: number; // 粒度 (μm)
  recovery: number; // 回收率 (%)
  purity: number; // 纯度 (%)
  density: number; // 密度 (g/cm³)
  ph: number; // pH值
  qualityGrade: 'A' | 'B' | 'C' | 'D';
  inspector: string;
  testMethod: string;
  status: 'passed' | 'failed' | 'pending' | 'retest';
  remarks?: string;
}

// 质量统计接口
interface QualityStats {
  totalSamples: number;
  passRate: number;
  avgPbGrade: number;
  avgZnGrade: number;
  avgRecovery: number;
  gradeACount: number;
  gradeBCount: number;
  gradeCCount: number;
  gradeDCount: number;
}

export default function ProductionQualityDataPage() {
  const router = useRouter();
  const [qualityData, setQualityData] = useState<QualityData[]>([]);
  const [stats, setStats] = useState<QualityStats | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedDate, setSelectedDate] = useState("");
  const [selectedGrade, setSelectedGrade] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  // 模拟数据加载
  useEffect(() => {
    const generateMockData = (): QualityData[] => {
      const data: QualityData[] = [];
      const productTypes = ['铅精矿', '锌精矿', '混合精矿', '铅块', '锌锭'];
      const inspectors = ['张检验', '李化验', '王质控', '赵分析', '陈测试'];
      const testMethods = ['XRF光谱法', '原子吸收法', '滴定法', '重量法', '电感耦合等离子体法'];
      const grades: ('A' | 'B' | 'C' | 'D')[] = ['A', 'B', 'C', 'D'];

      for (let i = 0; i < 80; i++) {
        const date = new Date(Date.now() - i * 6 * 60 * 60 * 1000); // 每6小时一次检测
        const productType = productTypes[Math.floor(Math.random() * productTypes.length)];

        // 根据产品类型设置不同的品位范围
        let pbGrade, znGrade;
        if (productType === '铅精矿') {
          pbGrade = 60 + Math.random() * 15; // 60-75%
          znGrade = 5 + Math.random() * 10; // 5-15%
        } else if (productType === '锌精矿') {
          pbGrade = 2 + Math.random() * 8; // 2-10%
          znGrade = 50 + Math.random() * 20; // 50-70%
        } else if (productType === '混合精矿') {
          pbGrade = 25 + Math.random() * 20; // 25-45%
          znGrade = 25 + Math.random() * 20; // 25-45%
        } else {
          pbGrade = 85 + Math.random() * 10; // 85-95%
          znGrade = 1 + Math.random() * 4; // 1-5%
        }

        const recovery = 80 + Math.random() * 15; // 80-95%
        const qualityGrade = recovery >= 90 ? 'A' : recovery >= 85 ? 'B' : recovery >= 80 ? 'C' : 'D';

        data.push({
          id: `QC${date.getFullYear()}${String(date.getMonth() + 1).padStart(2, '0')}${String(date.getDate()).padStart(2, '0')}-${String(i % 100).padStart(3, '0')}`,
          timestamp: date.toISOString(),
          batchNumber: `BATCH-${date.getFullYear()}${String(date.getMonth() + 1).padStart(2, '0')}${String(date.getDate()).padStart(2, '0')}-${String(i % 10 + 1).padStart(2, '0')}`,
          productType,
          pbGrade: Math.round(pbGrade * 100) / 100,
          znGrade: Math.round(znGrade * 100) / 100,
          moisture: Math.round((1 + Math.random() * 4) * 100) / 100, // 1-5%
          particleSize: Math.round((50 + Math.random() * 100) * 10) / 10, // 50-150μm
          recovery: Math.round(recovery * 100) / 100,
          purity: Math.round((95 + Math.random() * 4) * 100) / 100, // 95-99%
          density: Math.round((3.5 + Math.random() * 1.5) * 100) / 100, // 3.5-5.0 g/cm³
          ph: Math.round((6.5 + Math.random() * 2) * 100) / 100, // 6.5-8.5
          qualityGrade,
          inspector: inspectors[Math.floor(Math.random() * inspectors.length)],
          testMethod: testMethods[Math.floor(Math.random() * testMethods.length)],
          status: Math.random() > 0.85 ? 'failed' : Math.random() > 0.9 ? 'retest' : Math.random() > 0.05 ? 'passed' : 'pending',
          remarks: Math.random() > 0.8 ? '符合质量标准要求' : undefined
        });
      }

      return data.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    };

    const mockData = generateMockData();
    setQualityData(mockData);

    // 计算统计数据
    const passedData = mockData.filter(d => d.status === 'passed');
    const passRate = (passedData.length / mockData.length) * 100;
    const avgPbGrade = passedData.reduce((sum, d) => sum + d.pbGrade, 0) / passedData.length;
    const avgZnGrade = passedData.reduce((sum, d) => sum + d.znGrade, 0) / passedData.length;
    const avgRecovery = passedData.reduce((sum, d) => sum + d.recovery, 0) / passedData.length;

    setStats({
      totalSamples: mockData.length,
      passRate: Math.round(passRate * 100) / 100,
      avgPbGrade: Math.round(avgPbGrade * 100) / 100,
      avgZnGrade: Math.round(avgZnGrade * 100) / 100,
      avgRecovery: Math.round(avgRecovery * 100) / 100,
      gradeACount: mockData.filter(d => d.qualityGrade === 'A').length,
      gradeBCount: mockData.filter(d => d.qualityGrade === 'B').length,
      gradeCCount: mockData.filter(d => d.qualityGrade === 'C').length,
      gradeDCount: mockData.filter(d => d.qualityGrade === 'D').length
    });
  }, []);

  // 过滤数据
  const filteredData = qualityData.filter(item => {
    const matchesSearch = searchTerm === "" ||
      item.batchNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.productType.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.inspector.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesDate = selectedDate === "" ||
      item.timestamp.startsWith(selectedDate);

    const matchesGrade = selectedGrade === "" ||
      item.qualityGrade === selectedGrade;

    return matchesSearch && matchesDate && matchesGrade;
  });

  // 刷新数据
  const handleRefresh = () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  };

  // 导出数据
  const handleExport = () => {
    const csvContent = "data:text/csv;charset=utf-8," +
      "批次号,产品类型,铅品位,锌品位,水分,粒度,回收率,纯度,密度,pH值,质量等级,检验员,检测方法,状态\n" +
      filteredData.map(row =>
        `${row.batchNumber},${row.productType},${row.pbGrade},${row.znGrade},${row.moisture},${row.particleSize},${row.recovery},${row.purity},${row.density},${row.ph},${row.qualityGrade},${row.inspector},${row.testMethod},${row.status}`
      ).join("\n");

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", `生产质量数据_${new Date().toISOString().split('T')[0]}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'passed': return 'text-green-500';
      case 'failed': return 'text-red-500';
      case 'pending': return 'text-yellow-500';
      case 'retest': return 'text-blue-500';
      default: return 'text-gray-500';
    }
  };

  // 获取状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'passed': return '合格';
      case 'failed': return '不合格';
      case 'pending': return '待检';
      case 'retest': return '复检';
      default: return '未知';
    }
  };

  // 获取等级颜色
  const getGradeColor = (grade: string) => {
    switch (grade) {
      case 'A': return 'text-green-600 bg-green-50';
      case 'B': return 'text-blue-600 bg-blue-50';
      case 'C': return 'text-yellow-600 bg-yellow-50';
      case 'D': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-14 items-center justify-between px-4">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => router.back()}
              className="h-8 w-8"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-lg font-semibold">生产质量数据</h1>
          </div>
          <div className="flex items-center gap-2">
            <ThemeToggle />
            <HamburgerMenu />
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6 space-y-6">
        {/* 功能按钮区域 */}
        <div className="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
          <div>
            <h2 className="text-xl font-semibold">生产质量数据</h2>
            <p className="text-sm text-muted-foreground">实时监控生产质量指标</p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 mr-1 ${isLoading ? 'animate-spin' : ''}`} />
              刷新
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleExport}
            >
              <Download className="h-4 w-4 mr-1" />
              导出
            </Button>
          </div>
        </div>

        {/* 质量统计概览 */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
            <Card>
              <CardContent className="pt-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">总样本数</p>
                    <p className="text-2xl font-bold">{stats.totalSamples}</p>
                  </div>
                  <FlaskConical className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">合格率</p>
                    <p className="text-2xl font-bold text-green-500">{stats.passRate}%</p>
                  </div>
                  <CheckCircle className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">平均铅品位</p>
                    <p className="text-2xl font-bold">{stats.avgPbGrade}%</p>
                  </div>
                  <BarChart3 className="h-8 w-8 text-orange-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">平均锌品位</p>
                    <p className="text-2xl font-bold">{stats.avgZnGrade}%</p>
                  </div>
                  <BarChart3 className="h-8 w-8 text-purple-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">平均回收率</p>
                    <p className="text-2xl font-bold">{stats.avgRecovery}%</p>
                  </div>
                  <Target className="h-8 w-8 text-cyan-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">A级产品</p>
                    <p className="text-2xl font-bold text-green-600">{stats.gradeACount}</p>
                  </div>
                  <Award className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* 质量等级分布 */}
        {stats && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Gauge className="h-5 w-5" />
                质量等级分布
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">A级 (优秀)</span>
                    <Badge className="text-green-600 bg-green-50">
                      {stats.gradeACount}
                    </Badge>
                  </div>
                  <Progress value={(stats.gradeACount / stats.totalSamples) * 100} className="h-2" />
                  <div className="text-xs text-muted-foreground">
                    {((stats.gradeACount / stats.totalSamples) * 100).toFixed(1)}%
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">B级 (良好)</span>
                    <Badge className="text-blue-600 bg-blue-50">
                      {stats.gradeBCount}
                    </Badge>
                  </div>
                  <Progress value={(stats.gradeBCount / stats.totalSamples) * 100} className="h-2" />
                  <div className="text-xs text-muted-foreground">
                    {((stats.gradeBCount / stats.totalSamples) * 100).toFixed(1)}%
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">C级 (合格)</span>
                    <Badge className="text-yellow-600 bg-yellow-50">
                      {stats.gradeCCount}
                    </Badge>
                  </div>
                  <Progress value={(stats.gradeCCount / stats.totalSamples) * 100} className="h-2" />
                  <div className="text-xs text-muted-foreground">
                    {((stats.gradeCCount / stats.totalSamples) * 100).toFixed(1)}%
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">D级 (不合格)</span>
                    <Badge className="text-red-600 bg-red-50">
                      {stats.gradeDCount}
                    </Badge>
                  </div>
                  <Progress value={(stats.gradeDCount / stats.totalSamples) * 100} className="h-2" />
                  <div className="text-xs text-muted-foreground">
                    {((stats.gradeDCount / stats.totalSamples) * 100).toFixed(1)}%
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* 数据筛选 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Search className="h-5 w-5" />
              数据筛选
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="space-y-2">
                <Label htmlFor="search">搜索</Label>
                <Input
                  id="search"
                  placeholder="批次号/产品类型/检验员"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="date">选择日期</Label>
                <Input
                  id="date"
                  type="date"
                  value={selectedDate}
                  onChange={(e) => setSelectedDate(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="grade">质量等级</Label>
                <select
                  id="grade"
                  value={selectedGrade}
                  onChange={(e) => setSelectedGrade(e.target.value)}
                  className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm"
                >
                  <option value="">全部等级</option>
                  <option value="A">A级 (优秀)</option>
                  <option value="B">B级 (良好)</option>
                  <option value="C">C级 (合格)</option>
                  <option value="D">D级 (不合格)</option>
                </select>
              </div>
              <div className="space-y-2">
                <Label>&nbsp;</Label>
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchTerm("");
                    setSelectedDate("");
                    setSelectedGrade("");
                  }}
                  className="w-full"
                >
                  清除筛选
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 质量数据表格 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                质量检测记录
              </span>
              <Badge variant="secondary">
                共 {filteredData.length} 条记录
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>批次号</TableHead>
                    <TableHead>产品类型</TableHead>
                    <TableHead>铅品位(%)</TableHead>
                    <TableHead>锌品位(%)</TableHead>
                    <TableHead>水分(%)</TableHead>
                    <TableHead>回收率(%)</TableHead>
                    <TableHead>纯度(%)</TableHead>
                    <TableHead>质量等级</TableHead>
                    <TableHead>检验员</TableHead>
                    <TableHead>检测方法</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>检测时间</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredData.slice(0, 15).map((item) => (
                    <TableRow key={item.id}>
                      <TableCell className="font-medium">{item.batchNumber}</TableCell>
                      <TableCell>{item.productType}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          {item.pbGrade >= 60 ? (
                            <TrendingUp className="h-3 w-3 text-green-500" />
                          ) : item.pbGrade >= 40 ? (
                            <TrendingUp className="h-3 w-3 text-yellow-500" />
                          ) : (
                            <TrendingDown className="h-3 w-3 text-red-500" />
                          )}
                          <span>{item.pbGrade}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          {item.znGrade >= 50 ? (
                            <TrendingUp className="h-3 w-3 text-green-500" />
                          ) : item.znGrade >= 30 ? (
                            <TrendingUp className="h-3 w-3 text-yellow-500" />
                          ) : (
                            <TrendingDown className="h-3 w-3 text-red-500" />
                          )}
                          <span>{item.znGrade}</span>
                        </div>
                      </TableCell>
                      <TableCell>{item.moisture}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          {item.recovery >= 90 ? (
                            <TrendingUp className="h-3 w-3 text-green-500" />
                          ) : item.recovery >= 85 ? (
                            <TrendingUp className="h-3 w-3 text-yellow-500" />
                          ) : (
                            <TrendingDown className="h-3 w-3 text-red-500" />
                          )}
                          <span>{item.recovery}</span>
                        </div>
                      </TableCell>
                      <TableCell>{item.purity}</TableCell>
                      <TableCell>
                        <Badge className={getGradeColor(item.qualityGrade)}>
                          {item.qualityGrade}级
                        </Badge>
                      </TableCell>
                      <TableCell>{item.inspector}</TableCell>
                      <TableCell>{item.testMethod}</TableCell>
                      <TableCell>
                        <span className={getStatusColor(item.status)}>
                          {getStatusText(item.status)}
                        </span>
                      </TableCell>
                      <TableCell>{new Date(item.timestamp).toLocaleString()}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {filteredData.length > 15 && (
              <div className="mt-4 text-center">
                <Button variant="outline">
                  加载更多数据
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* 底部签名 */}
      <Footer />
    </div>
  );
}