# 组件化架构实施指南

## 🎯 快速开始

### 创建新页面的标准流程

#### 步骤 1：创建路由页面文件
```bash
# 创建路由目录和文件
mkdir app/new-feature
touch app/new-feature/page.tsx
```

```typescript
// app/new-feature/page.tsx
import { NewFeaturePage } from "@/components/new-feature-page";
import { AuthGuard } from "@/components/auth-guard";

export default function Page() {
  return (
    <AuthGuard requireAuth={true}>
      <NewFeaturePage />
    </AuthGuard>
  );
}
```

#### 步骤 2：创建业务组件文件
```bash
# 创建组件文件
touch components/new-feature-page.tsx
```

```typescript
// components/new-feature-page.tsx
"use client";

import React, { useState, useCallback, memo } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

// 类型定义
interface NewFeaturePageProps {
  mode?: 'full' | 'mini' | 'readonly';
  onDataChange?: (data: any) => void;
}

// 组件实现
export const NewFeaturePage = memo(({ 
  mode = 'full',
  onDataChange 
}: NewFeaturePageProps) => {
  // 状态管理
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);

  // 事件处理
  const handleSubmit = useCallback(async () => {
    setLoading(true);
    try {
      // 业务逻辑实现
      console.log('提交数据:', data);
      onDataChange?.(data);
    } catch (error) {
      console.error('提交失败:', error);
    } finally {
      setLoading(false);
    }
  }, [data, onDataChange]);

  // 渲染逻辑
  return (
    <div className="container mx-auto p-4">
      <Card>
        <CardHeader>
          <CardTitle>新功能页面</CardTitle>
        </CardHeader>
        <CardContent>
          <Button 
            onClick={handleSubmit} 
            disabled={loading}
          >
            {loading ? '提交中...' : '提交'}
          </Button>
        </CardContent>
      </Card>
    </div>
  );
});

NewFeaturePage.displayName = 'NewFeaturePage';
```

#### 步骤 3：配置路由（如需要）
```typescript
// lib/route-config.ts
export const routes = [
  // ... 其他路由
  {
    path: '/new-feature',
    name: 'new-feature',
    authStrategy: AuthStrategy.SIMPLE,
    pageType: 'business' as const,
    title: '新功能',
    description: '新功能页面描述'
  },
];
```

## 🔧 开发规范

### 1. 文件命名规范

```bash
# 路由页面文件
app/feature-name/page.tsx

# 业务组件文件
components/feature-name-page.tsx

# 子组件文件
components/feature-name-card.tsx
components/feature-name-dialog.tsx

# 工具函数文件
lib/feature-name-utils.ts

# 类型定义文件
types/feature-name.ts
```

### 2. 组件结构模板

```typescript
// 标准组件结构模板
"use client";

import React, { useState, useCallback, memo, useEffect } from "react";
// UI组件导入
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
// 工具函数导入
import { cn } from "@/lib/utils";
// 类型导入
import type { ComponentProps } from "@/types/component";

// 1. 类型定义
interface ComponentPageProps {
  mode?: 'full' | 'mini' | 'readonly';
  className?: string;
  onSubmit?: (data: any) => Promise<void>;
  onCancel?: () => void;
}

// 2. 组件实现
export const ComponentPage = memo(({ 
  mode = 'full',
  className,
  onSubmit,
  onCancel 
}: ComponentPageProps) => {
  // 3. 状态管理
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // 4. 副作用处理
  useEffect(() => {
    // 组件挂载时的初始化逻辑
    return () => {
      // 组件卸载时的清理逻辑
    };
  }, []);

  // 5. 事件处理函数
  const handleSubmit = useCallback(async () => {
    if (!data) return;
    
    setLoading(true);
    setError(null);
    
    try {
      await onSubmit?.(data);
    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  }, [data, onSubmit]);

  // 6. 渲染逻辑
  if (error) {
    return (
      <div className="error-container">
        <p>发生错误: {error.message}</p>
        <Button onClick={() => setError(null)}>重试</Button>
      </div>
    );
  }

  return (
    <div className={cn("component-container", className)}>
      <Card>
        {/* 组件内容 */}
        <Button 
          onClick={handleSubmit} 
          disabled={loading}
        >
          {loading ? '处理中...' : '提交'}
        </Button>
      </Card>
    </div>
  );
});

// 7. 组件显示名称
ComponentPage.displayName = 'ComponentPage';
```

### 3. 路由页面模板

```typescript
// 标准路由页面模板
import { ComponentPage } from "@/components/component-page";
import { AuthGuard } from "@/components/auth-guard";
import { ErrorBoundary } from "@/components/error-boundary";
import { Suspense } from "react";
import { LoadingTransition } from "@/components/loading-transition";

export default function Page() {
  return (
    <ErrorBoundary>
      <AuthGuard requireAuth={true}>
        <Suspense fallback={<LoadingTransition />}>
          <ComponentPage />
        </Suspense>
      </AuthGuard>
    </ErrorBoundary>
  );
}
```

## 🎨 常用模式

### 1. 表单页面模式

```typescript
export const FormPage = memo(() => {
  const [formData, setFormData] = useState(initialFormData);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const updateFormField = useCallback((field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  }, []);

  const handleSubmit = useCallback(async () => {
    setIsSubmitting(true);
    try {
      // 提交逻辑
      setSubmitStatus('success');
    } catch (error) {
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  }, [formData]);

  return (
    <form onSubmit={handleSubmit}>
      {/* 表单内容 */}
    </form>
  );
});
```

### 2. 数据展示页面模式

```typescript
export const DataPage = memo(() => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const response = await api.getData();
      setData(response.data);
    } catch (err) {
      setError(err);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  if (loading) return <LoadingTransition />;
  if (error) return <ErrorDisplay error={error} onRetry={fetchData} />;

  return (
    <div>
      {/* 数据展示内容 */}
    </div>
  );
});
```

### 3. 对话框组件模式

```typescript
export const DialogPage = memo(() => {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);

  const handleItemClick = useCallback((item) => {
    setSelectedItem(item);
    setDialogOpen(true);
  }, []);

  const handleDialogClose = useCallback(() => {
    setDialogOpen(false);
    setSelectedItem(null);
  }, []);

  return (
    <div>
      {/* 主要内容 */}
      
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent>
          {/* 对话框内容 */}
        </DialogContent>
      </Dialog>
    </div>
  );
});
```

## 🔍 调试和测试

### 1. 组件调试

```typescript
// 开发环境调试信息
export const DebugInfo = memo(({ componentName, data }) => {
  if (process.env.NODE_ENV !== 'development') return null;
  
  return (
    <div className="debug-info">
      <h4>调试信息 - {componentName}</h4>
      <pre>{JSON.stringify(data, null, 2)}</pre>
    </div>
  );
});

// 在组件中使用
export const ComponentPage = memo(() => {
  const [data, setData] = useState(null);
  
  return (
    <div>
      {/* 组件内容 */}
      <DebugInfo componentName="ComponentPage" data={data} />
    </div>
  );
});
```

### 2. 性能监控

```typescript
// 性能监控 Hook
const usePerformanceMonitor = (componentName: string) => {
  const renderCount = useRef(0);
  const startTime = useRef(Date.now());
  
  useEffect(() => {
    renderCount.current += 1;
    const renderTime = Date.now() - startTime.current;
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`${componentName} 渲染次数: ${renderCount.current}, 渲染时间: ${renderTime}ms`);
    }
  });
  
  return { renderCount: renderCount.current };
};

// 在组件中使用
export const ComponentPage = memo(() => {
  const { renderCount } = usePerformanceMonitor('ComponentPage');
  
  return (/* 组件内容 */);
});
```

### 3. 单元测试

```typescript
// __tests__/component-page.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ComponentPage } from '@/components/component-page';

describe('ComponentPage', () => {
  it('应该正确渲染组件', () => {
    render(<ComponentPage />);
    expect(screen.getByText('组件标题')).toBeInTheDocument();
  });

  it('应该处理用户交互', async () => {
    const onSubmit = jest.fn();
    render(<ComponentPage onSubmit={onSubmit} />);
    
    fireEvent.click(screen.getByText('提交'));
    
    await waitFor(() => {
      expect(onSubmit).toHaveBeenCalled();
    });
  });

  it('应该正确处理错误状态', () => {
    const error = new Error('测试错误');
    render(<ComponentPage error={error} />);
    
    expect(screen.getByText('发生错误: 测试错误')).toBeInTheDocument();
  });
});
```

## ⚠️ 常见问题

### 1. 组件导入错误
```typescript
// ❌ 错误的导入方式
import ShiftSamplePage from "@/components/shift-sample-page";

// ✅ 正确的导入方式
import { ShiftSamplePage } from "@/components/shift-sample-page";
```

### 2. 状态管理问题
```typescript
// ❌ 避免在路由页面中管理状态
export default function Page() {
  const [data, setData] = useState(); // ❌
  return <ComponentPage data={data} />;
}

// ✅ 在业务组件中管理状态
export const ComponentPage = memo(() => {
  const [data, setData] = useState(); // ✅
  return (/* 组件内容 */);
});
```

### 3. 性能优化遗漏
```typescript
// ❌ 缺少 memo 优化
export const ComponentPage = () => {
  return (/* 组件内容 */);
};

// ✅ 使用 memo 优化
export const ComponentPage = memo(() => {
  return (/* 组件内容 */);
});
```

## 🎯 检查清单

### 新页面开发检查清单
- [ ] 路由页面文件大小 < 20 行
- [ ] 业务组件使用 memo 包装
- [ ] 组件 Props 有完整类型定义
- [ ] 包含错误处理机制
- [ ] 添加 displayName 属性
- [ ] 遵循命名规范
- [ ] 包含必要的注释
- [ ] 通过 ESLint 检查
- [ ] 通过 TypeScript 检查
- [ ] 编写基础单元测试

这个实施指南为团队提供了标准化的开发流程和最佳实践，确保所有开发者都能正确实施组件化架构。
