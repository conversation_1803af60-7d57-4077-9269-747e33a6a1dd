总是以中文进行响应。

# Augment 通用快捷键指南

## 1. 规则管理系统

### 基础命令
**基础语法**: `/rule`

### 1.1 规则应用 - `/rule as` 或 `/rule=`

**核心功能**: 应用规则集

**功能描述**: 加载并执行指定路径文件中定义的操作规范与指令集

**语法格式**:
- **标准语法**: `/rule as <file_path>` 或 `/rule=<file_path>`
  - **说明**: 在当前工作目录中应用指定的规则文件
  - **示例**: `/rule as project-guidelines.md`

- **扩展语法**: `/rule as <file_path_X> in <directory_Y>`
  - **说明**: 在目标目录 `<directory_Y>` 下，应用 `<file_path_X>` 中定义的规则集
  - **示例**: `/rule as coding-standards.md in ./docs/guidelines`

### 1.2 规则导入 - `/rule add` 或 `/rule+`

**核心功能**: 导入规则文件

**功能描述**: 将指定路径的规则文件复制并集成到目标工作目录中，以便于本地化调用与管理

**语法格式**:
- **标准语法**: `/rule add <file_path>`
  - **说明**: 将规则文件导入到当前工作目录的 `.augment-guidelines` 子目录下
  - **示例**: `/rule add ./templates/api-rules.md`

- **扩展语法**: `/rule add <file_path_X> to <directory_Y>`
  - **说明**: 将规则文件 `<file_path_X>` 复制并集成到目标目录 `<directory_Y>` 中
  - **示例**: `/rule add security-rules.md to ./project/config`

- **批量导入语法**: `/rule add <file1> <file2> <file3> ...`
  - **说明**: 同时导入多个规则文件到目标目录
  - **示例**: `/rule add coding-style.md security-rules.md api-guidelines.md`

- **目录导入语法**: `/rule add <directory>`
  - **说明**: 导入指定目录下所有规则文件
  - **示例**: `/rule add ./rule-templates/`

**批量规则导入机制**:
1. **并行处理**: 多个规则文件可同时导入，无需考虑顺序依赖
2. **冲突检测**: 自动检测同名规则文件并提供覆盖选项
3. **错误隔离**: 单个文件导入失败不影响其他文件的正常导入

### 1.3 规则优化 - `/rule up` 或 `/rule-`

**核心功能**: 迭代优化关联规则文件

**功能描述**: 基于上下文语义及用户反馈，系统将自动分析并优化现有的规则文件，以提升其适用性和效率

**语法格式**:
- **标准语法**: `/rule up <file_path>`
  - **说明**: 对指定的规则文件执行自动化分析与优化
  - **示例**: `/rule up ./guidelines/style-guide.md`

- **扩展语法**: `/rule up <file_path> as <reference_material>`
  - **说明**: 结合提供的外部参考资料 `<reference_material>`，对指定规则文件进行深度优化与增强
  - **示例**: `/rule up coding-standards.md as ./docs/best-practices.md`

## 2. 任务管理系统

### 基础命令
**基础语法**: `/task`

### 2.1 任务应用 - `/task as` 或 `/task=`

**核心功能**: 添加任务集

**功能描述**: 使用 Augment Task 功能（Add New Tasks & Run Tasks）添加并执行指定路径文件中定义的任务

**语法格式**:
- **标准语法**: `/task as <file_path>` 或 `/task=<file_path>`
  - **说明**: 在当前工作目录中应用指定的任务文件
  - **示例**: `/task as deployment-tasks.md`

- **扩展语法**: `/task as <file_path_X> in <directory_Y>`
  - **说明**: 在目标目录 `<directory_Y>` 下，应用 `<file_path_X>` 中定义的任务流
  - **示例**: `/task as build-process.md in ./scripts/automation`

### 2.2 任务导入 - `/task add` 或 `/task+`

**核心功能**: 导入任务文件

**功能描述**: 使用 Augment Task 功能（Add New Tasks）添加指定路径文件中定义的任务

**语法格式**:
- **标准语法**: `/task add <file_path>`
  - **说明**: 将任务文件导入到当前任务序列中（默认为最新的任务序号）
  - **示例**: `/task add ./workflows/testing-tasks.md`

- **批量导入语法**: `/task add <file1> <file2> <file3> ...`
  - **说明**: 按照指定顺序依次导入多个任务文件，系统将自动安排任务执行流程
  - **执行顺序**: 严格按照命令中文件的排列顺序进行导入和执行
  - **示例**: `/task add setup.md build.md test.md deploy.md`

- **目录导入语法**: `/task add <directory>`
  - **说明**: 导入指定目录下所有任务文件（支持 .md, .txt 等格式）
  - **排序规则**: 按文件名字母顺序自动排序导入
  - **示例**: `/task add ./workflows/`

**批量任务导入机制**:
1. **顺序保证**: 多文件导入时严格按照指定顺序或文件名排序执行
2. **依赖处理**: 自动识别任务间的依赖关系，确保前置任务完成后再执行后续任务
3. **错误处理**: 单个文件导入失败不影响其他文件的正常导入

### 2.3 任务优化 - `/task up` 或 `/task-`

**核心功能**: 迭代优化关联任务文件

**功能描述**: 基于上下文语义及用户反馈，系统将自动分析并优化现有的任务文件并同步到 Augment Task 中，以提升其适用性和效率

**语法格式**:
- **标准语法**: `/task up <file_path>`
  - **说明**: 对指定的任务文件执行自动化分析与优化
  - **示例**: `/task up ./tasks/development-workflow.md`

- **扩展语法**: `/task up <file_path> as <reference_material>`
  - **说明**: 结合提供的外部参考资料 `<reference_material>`，对指定任务文件进行深度优化与增强并同步到 Augment Task 中
  - **示例**: `/task up project-tasks.md as ./docs/requirements.md`

## 3. 泛文档管理系统

### 基础命令
**基础语法**: `/文档格式`（如 `/md`、`/csv`、`/json`、`/mermaid` 等）

### 3.1 文档应用 - `/文档格式 as` 或 `/文档格式=`

**核心功能**: 应用文档内容

**功能描述**: 加载并执行指定路径文件中定义的内容说明

**语法格式**:
- **标准语法**: `/文档格式 as <file_path>` 或 `/文档格式=<file_path>`
  - **说明**: 在当前工作目录中应用指定格式的文件
  - **示例**: `/md as documentation.md`、`/json as config.json`

- **扩展语法**: `/文档格式 as <file_path_X> in <directory_Y>`
  - **说明**: 在目标目录 `<directory_Y>` 下，应用 `<file_path_X>` 中定义的内容说明
  - **示例**: `/csv as data-schema.csv in ./database/schemas`

### 3.2 文档导入 - `/文档格式 add` 或 `/文档格式+`

**核心功能**: 导入指定格式的文件

**功能描述**: 将指定路径的指定格式文件复制并集成到目标工作目录中，以便于本地化调用与管理

**语法格式**:
- **标准语法**: `/文档格式 add <file_path>`
  - **说明**: 将指定格式文件导入到当前工作目录的 `.augment-guidelines` 子目录下
  - **示例**: `/mermaid add ./diagrams/architecture.mmd`

- **扩展语法**: `/文档格式 add <file_path_X> to <directory_Y>`
  - **说明**: 将指定格式文件 `<file_path_X>` 复制并集成到目标目录 `<directory_Y>` 中
  - **示例**: `/json add api-spec.json to ./docs/api`

- **批量导入语法**: `/文档格式 add <file1> <file2> <file3> ...`
  - **说明**: 同时导入多个相同格式的文件
  - **示例**: `/md add readme.md changelog.md contributing.md`

- **目录导入语法**: `/文档格式 add <directory>`
  - **说明**: 导入指定目录下所有匹配格式的文件
  - **示例**: `/csv add ./data-exports/`

**批量文档导入机制**:
1. **格式筛选**: 目录导入时自动筛选匹配指定格式的文件
2. **并行处理**: 多个文档文件可同时导入，提高处理效率
3. **版本管理**: 自动处理同名文件的版本冲突和备份

### 3.3 文档优化 - `/文档格式 up` 或 `/文档格式-`

**核心功能**: 迭代优化关联指定格式文件

**功能描述**: 基于上下文语义及用户反馈，系统将自动分析并优化现有的指定格式文件，以提升其适用性和效率

**语法格式**:
- **标准语法**: `/文档格式 up <file_path>`
  - **说明**: 对指定的文档格式文件执行自动化分析与优化
  - **示例**: `/md up ./docs/user-guide.md`

- **扩展语法**: `/文档格式 up <file_path> as <reference_material>`
  - **说明**: 结合提供的外部参考资料 `<reference_material>`，对指定格式文件进行深度优化与增强
  - **示例**: `/csv up data-model.csv as ./specs/database-requirements.md`

## 4. 快速执行系统

### 基础命令
**基础语法**: `/cast`

### 4.1 快速执行 - `/cast`

**核心功能**: 通用文件内容执行器

**功能描述**: 在当前工作区智能识别并执行指定文件路径中的内容，支持自动检测文件类型（规则文件、任务文件或各种格式文档）并采用相应的执行策略

**语法格式**:
- **标准语法**: `/cast <file_path>`
  - **说明**: 在当前工作目录中执行指定文件的内容
  - **自动识别**: 系统将根据文件内容和扩展名自动判断执行方式
  - **示例**: `/cast project-setup.md`（自动识别为任务文件并执行）

- **扩展语法**: `/cast <file_path> as <type>`
  - **说明**: 强制指定文件类型进行执行
  - **支持类型**: `rule`（规则）、`task`（任务）、`doc`（文档）
  - **示例**: `/cast config.txt as rule`

**执行逻辑**:
1. **文件类型检测**: 自动分析文件内容结构和语义
2. **智能路由**: 根据检测结果调用相应的执行引擎
   - 规则文件 → 应用规则集（等同于 `/rule as`）
   - 任务文件 → 添加并执行任务（等同于 `/task as`）
   - 文档文件 → 应用文档内容（等同于 `/文档格式 as`）
3. **错误处理**: 无法识别时提示用户手动指定类型

## 5. 使用注意事项

### 5.1 路径规范
- 支持相对路径和绝对路径
- 建议使用相对路径以提高可移植性
- 路径中包含空格时需要使用引号包围

### 5.2 文件格式支持
- **文档格式**: md, txt, csv, json, yaml, xml
- **图表格式**: mermaid, plantuml, drawio
- **配置格式**: ini, conf, env, properties
- **代码格式**: js, ts, py, java, cpp, etc.

### 5.3 最佳实践
1. **命名规范**: 使用描述性的文件名和路径
2. **版本控制**: 重要规则和任务文件应纳入版本控制
3. **文档维护**: 定期使用 `up` 命令优化文件内容
4. **目录结构**: 建议在项目根目录创建 `.augment-guidelines` 目录统一管理

---